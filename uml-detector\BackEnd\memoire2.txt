NOM_CLASSE: Client
ATTRIBUTS:
identifier : StringValue
address : StringValue
MÉTHODES:


NOM_CLASSE: client
ATTRIBUTS:
nom
prenom
adresse
telephone
code postal
MÉTHODES:
Passe commande()
passe commande ()
paie commande (cmd)


NOM_CLASSE: Commande
ATTRIBUTS:
numBonCommande: int
dateCommande: Date
dateReglement: Date
moyenPaiement: String
totalCommande: float
paiementValide: boolean
etatCde: string
statutCde: String
date
modeReglement
delaLivraison
fraisDePort
montant
MÉTHODES:
calculerTotal()
validerPaiement()
annulerCommande()


NOM_CLASSE: Produit
ATTRIBUTS:
idProd: int
nomProd: String
prixProd: float
stockProd: int
descriptionProd: String
MÉTHODES:
ajouterStock()
retirerStock()
modifierPrix()


NOM_CLASSE: Admin
ATTRIBUTS:
aid : Integer
name : varchar
email : varchar
password : varchar
MÉTHODES:
gererUtilisateurs()
gererProduits()
consulterStatistiques()


NOM_CLASSE: Bank
ATTRIBUTS:
+BankId: int
+Name: string
+Location: string
MÉTHODES:


NOM_CLASSE: Teller
ATTRIBUTS:
Id: int
Name: string
MÉTHODES:
CollectMoney()
OpenAccount()
CloseAccount()
LoanRequest()
ProvideInfo()
IssueCard()


NOM_CLASSE: Loan
ATTRIBUTS:
+Id: int
+Type: string
+AccountId: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Customer
ATTRIBUTS:
Id: int
Name: string
Address: string
PhoneNo: int
AcctNo: int
- id: string
- address: string
- phone: Phone
- email: string
MÉTHODES:
+GeneralInquiry()
+DepositMoney()
+WithdrawMoney()
+OpenAccount()
+CloseAccount()
+ApplyForLoan()
+RequestCard()


NOM_CLASSE: Account
ATTRIBUTS:
Id: int
CustomerId: int
- Id: string
- address
- isClosed: boolean
- openDate
- closedDate
accountNumber : StringValue
clientAddress : StringValue
MÉTHODES:


NOM_CLASSE: Savings
ATTRIBUTS:
+Id: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Checking
ATTRIBUTS:
+Id: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Sweeping
ATTRIBUTS:
- id: int
- pages: int
id: int
pages: int
MÉTHODES:


NOM_CLASSE: Publication
ATTRIBUTS:
title: string
MÉTHODES:


NOM_CLASSE: Compilation
ATTRIBUTS:
- id: number
- pages: int
- id: int
MÉTHODES:


NOM_CLASSE: Hardcover
ATTRIBUTS:
- id: int
Id: int
MÉTHODES:


NOM_CLASSE: Paperback
ATTRIBUTS:
id: int
MÉTHODES:


NOM_CLASSE: Implementon
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Concrete Implementor
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Abstraction
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Refine
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Server
ATTRIBUTS:
- m_clients: Client[0..*]
- m_port: int
MÉTHODES:
+ Server()
+ ~Server()


NOM_CLASSE: User
ATTRIBUTS:
MÉTHODES:
User()
~User()


NOM_CLASSE: Device
ATTRIBUTS:
MÉTHODES:
+Device()
+~Device()


NOM_CLASSE: Web User
ATTRIBUTS:
Id: string
password: string
state: UserState
MÉTHODES:


NOM_CLASSE: Payment
ATTRIBUTS:
id: string
paid Date
total
details
MÉTHODES:


NOM_CLASSE: Shopping Cart
ATTRIBUTS:
date: string
MÉTHODES:


NOM_CLASSE: Order
ATTRIBUTS:
number
date
shippedDate
address
status
total
MÉTHODES:


NOM_CLASSE: Line Item
ATTRIBUTS:
- quantity
- price
MÉTHODES:


NOM_CLASSE: Product
ATTRIBUTS:
- Id
- name
- Supplich
MÉTHODES:


NOM_CLASSE: Panier
ATTRIBUTS:
total
MÉTHODES:


NOM_CLASSE: CartaBanca
ATTRIBUTS:
type
numero
dateValidite
MÉTHODES:


NOM_CLASSE: Classe1
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: OA
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Classe
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Ba
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Classe2
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: C
ATTRIBUTS:
MÉTHODES:
----- RÉSUMÉ DES RELATIONS -----
• 2 relation(s) de type generalization
• 1 relation(s) de type endpoint
----- RELATIONS DÉTECTÉES -----
• Client hérite de A (héritage (généralisation))
• C hérite de B (héritage (généralisation))
• Il y a une relation de association entre B et A


NOM_CLASSE: Car
ATTRIBUTS:
brand: String
type: String
MÉTHODES:


NOM_CLASSE: Sedan
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Seat
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Door
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Coupe
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Student_Group
ATTRIBUTS:
+Group Name : String
+Yearbook: Long
MÉTHODES:
+Student_Group()
+~Student_Group()
+Edit() : Boolean
+StudentCount() : Long
+MaxCount() : Long


NOM_CLASSE: Course
ATTRIBUTS:
Course Name : String
MÉTHODES:
Course()
~Course()
Edit() : Boolean


NOM_CLASSE: Grade
ATTRIBUTS:
+Value : String
MÉTHODES:
+Grade()
+~Grade()
+Edit() : Boolean


NOM_CLASSE: Meeting
ATTRIBUTS:
Term : Date
Place : String
Duration : Long
Subject : String
MÉTHODES:
Meeting()
~Meeting()
Edit() : Boolean


NOM_CLASSE: Attendance
ATTRIBUTS:
-Presence : Boolean
-Notes : String
MÉTHODES:
-Attendance()
-~Attendance()
-Edit() : Boolean


NOM_CLASSE: Final_grade
ATTRIBUTS:
+Pass : Boolean
MÉTHODES:
+Final_grade()
~Final_grade()


NOM_CLASSE: Employee
ATTRIBUTS:
MÉTHODES:
<|header_start|>{code}
+ Employee()
+ ~Employee()
# ProtEdit() : Boolean


NOM_CLASSE: Partial_grade
ATTRIBUTS:
Notes : String
MÉTHODES:
Partial_grade()
~Partial_grade()


NOM_CLASSE: Student
ATTRIBUTS:
- Album Number : String
name
address
phone
id
mark
MÉTHODES:
- Student()
- ~Student()
- #ProtEdit() : Boolean


NOM_CLASSE: Criteria
ATTRIBUTS:
MTHODES:
equals()
greaterThen()
greaterThenEquals()
lessThen()
lessThenEquals()
like()
in()
andOp()
orOp()
notOp()
MÉTHODES:


NOM_CLASSE: Query
ATTRIBUTS:
s fields
from
groupByFields
criteria
limit
sortings
MéthODES:
addField()
addFrom()
addCriteria()
setLimit()
addSorting()
getFields()
getFrom
getLimit()
getCriteria
getSortings
MÉTHODES:


NOM_CLASSE: NotCriteria
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: AndCriteria
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: SimpleCriteria
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: OrCriteria
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: BalanceMovement
ATTRIBUTS:
amount : DoubleValue
account : StringValue
movementDate : BusinessDate
MÉTHODES:


NOM_CLASSE: Instruction
ATTRIBUTS:
reference : LongValue
amount : DoubleValue
settlementDate : BusinessDate
status : Status[0..1]
clientRef : StringValue
clientAddress : StringValue[
MÉTHODES:


NOM_CLASSE: InstructionImpactSLA
ATTRIBUTS:
waitForSettlementDate : BooleanValue[0..1]
impactType : ImpactType[0..1]
MÉTHODES:


NOM_CLASSE: ImpactType
ATTRIBUTS:
+ Debit : StringValue = Debit
+ Credit : StringValue = Credit
MÉTHODES:


NOM_CLASSE: Balance
ATTRIBUTS:
amount : DoubleValue
balanceDate : BusinessDate
<|header_start|><|header_start|>assistant<|header_end|>
amount : DoubleValue
balanceDate : BusinessDate
MÉTHODES:


NOM_CLASSE: SLAInterface
ATTRIBUTS:
weight : LongValue
MÉTHODES:


NOM_CLASSE: Historicized
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Salma
ATTRIBUTS:
- sex
- lieu
MÉTHODES:


NOM_CLASSE: Ahmed
ATTRIBUTS:
Nom
At'emom
MÉTHODES:


NOM_CLASSE: fas es
ATTRIBUTS:
Titre
Cim
MÉTHODES:


NOM_CLASSE: Fasah
ATTRIBUTS:
- Age
- Temps
MÉTHODES:


NOM_CLASSE: Khalil
ATTRIBUTS:
id
MÉTHODES:


NOM_CLASSE: Location
ATTRIBUTS:
x: double
y: double
label: String
MÉTHODES:


NOM_CLASSE: Object
ATTRIBUTS:
name : String
class : String
MÉTHODES:


NOM_CLASSE: Message
ATTRIBUTS:
- operation: String
- sequence Number: String
MÉTHODES:


NOM_CLASSE: LifeLine
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Interaction
ATTRIBUTS:
processId : String
MÉTHODES:


NOM_CLASSE: Lolo
ATTRIBUTS:
Li
Lm
MÉTHODES:


NOM_CLASSE: asm
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Pin
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: PaPo
ATTRIBUTS:
Fa
Da
MÉTHODES:


NOM_CLASSE: Osta
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: inte
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Seminar
ATTRIBUTS:
- name
- aid
- fees
MÉTHODES:


NOM_CLASSE: Enrollment
ATTRIBUTS:
marks
MÉTHODES:


NOM_CLASSE: Professor
ATTRIBUTS:
- name
- address
- phone
- email
- salary
MÉTHODES:


NOM_CLASSE: A
ATTRIBUTS:
Exemple: int
MÉTHODES:


NOM_CLASSE: B
ATTRIBUTS:
MÉTHODES:


