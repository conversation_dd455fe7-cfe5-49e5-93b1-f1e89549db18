class 1:
NOM_CLASSE: Seminar
ATTRIBUTS: 
- name
- aid
- fees
MÉTHODES:

class 2:
NOM_CLASSE: Student
ATTRIBUTS: 
name
address
phone
id
mark
MÉTHODES:

class 3:
NOM_CLASSE: Enrollment
ATTRIBUTS: 
marks
MÉTHODES:

class 4:
NOM_CLASSE: Professor
ATTRIBUTS: 
- name
- address
- phone
- email
- salary
MÉTHODES:



----- RÉSUMÉ DES RELATIONS -----
• 4 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Flèche arrivant à Seminar sans classe de départ identifiée (relation orpheline)
• Il y a une relation de association entre Seminar et Professor
• Il y a une relation de association entre Enrollment et Seminar
• Il y a une relation de association entre Student et Enrollment
• Il y a une relation de association entre Enrollment et Seminar