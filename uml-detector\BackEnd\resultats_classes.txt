class 1:

NOM_CLASSE: A

ATTRIBUTS: 

Exemple: int

MÉTHODES:



class 2:

NOM_CLASSE: Client

ATTRIBUTS: 


identifier : StringValue
address : StringValue
MÉTHODES:



class 3:

NOM_CLASSE: B

ATTRIBUTS: 

MÉTHODES: Ajouter()



class 4:

NOM_CLASSE: C

ATTRIBUTS: 

MÉTHODES:







----- RÉSUMÉ DES RELATIONS -----

• 2 relation(s) de type generalization

• 1 relation(s) de type endpoint



----- RELATIONS DÉTECTÉES -----

• Client hérite de A (héritage (généralisation))

• C hérite de B (héritage (généralisation))

• Il y a une relation de association entre B et A