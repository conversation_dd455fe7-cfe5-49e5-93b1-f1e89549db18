=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 9 boîtes détectées
  Détection: class, confiance: 0.95
  [OK] Classe acceptée avec confiance 0.95 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: arrow, confiance: 0.64
  [OK] Flèche acceptée avec confiance 0.64 >= 0.4
  Détection: arrow, confiance: 0.58
  [OK] Flèche acceptée avec confiance 0.58 >= 0.4
  Détection: arrow, confiance: 0.51
  [OK] Flèche acceptée avec confiance 0.51 >= 0.4
  Détection: arrow, confiance: 0.34
  [!] Confiance trop basse pour arrow: 0.34 < 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 8
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 8 boîtes détectées
  Box 0: coords=1454,367,1511,422 class_idx=3 conf=0.59
  Détection: endpoin, confiance: 0.59
  Box 1: coords=1847,1639,1914,1707 class_idx=3 conf=0.55
  Détection: endpoin, confiance: 0.55
  Box 2: coords=1852,2162,1923,2231 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 3: coords=270,967,335,1035 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 4: coords=1823,429,1890,498 class_idx=3 conf=0.52
  Détection: endpoin, confiance: 0.52
  Box 5: coords=609,343,676,404 class_idx=3 conf=0.52
  Détection: endpoin, confiance: 0.52
  Box 6: coords=1815,999,1890,1074 class_idx=3 conf=0.50
  Détection: endpoin, confiance: 0.50
  Box 7: coords=1436,1409,1509,1477 class_idx=3 conf=0.45
  Détection: endpoin, confiance: 0.45

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 4, 'arrow': 4}
  Modèle 2: {'endpoin': 8}

=== FIN DÉTECTION ===
