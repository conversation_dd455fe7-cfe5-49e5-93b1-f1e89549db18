=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 7 boîtes détectées
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: class, confiance: 0.90
  [OK] Classe acceptée avec confiance 0.90 >= 0.25
  Détection: arrow, confiance: 0.78
  [OK] Flèche acceptée avec confiance 0.78 >= 0.4
  Détection: arrow, confiance: 0.75
  [OK] Flèche acceptée avec confiance 0.75 >= 0.4
  Détection: arrow, confiance: 0.69
  [OK] Flèche acceptée avec confiance 0.69 >= 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 6
  Classe détectée: generalization
  Classe détectée: generalization
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 6 boîtes détectées
  Box 0: coords=382,1167,512,1275 class_idx=2 conf=0.71
  Détection: generalization, confiance: 0.71
  Box 1: coords=2105,1150,2250,1286 class_idx=2 conf=0.69
  Détection: generalization, confiance: 0.69
  Box 2: coords=1828,795,1909,873 class_idx=3 conf=0.44
  Détection: endpoin, confiance: 0.44
  Box 3: coords=2148,1756,2241,1847 class_idx=3 conf=0.42
  Détection: endpoin, confiance: 0.42
  Box 4: coords=398,1608,492,1704 class_idx=3 conf=0.41
  Détection: endpoin, confiance: 0.41
  Box 5: coords=673,740,785,843 class_idx=3 conf=0.29
  Détection: endpoin, confiance: 0.29

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 4, 'arrow': 3}
  Modèle 2: {'generalization': 2, 'endpoin': 4}

=== FIN DÉTECTION ===
Fichier mémoire mis à jour avec 86 classes

Mise à jour du texte et mémoire: class 1:

NOM_CLASSE: A

ATTRIBUTS: 

Exemple: int

MÉTHODES:



class 2:

NOM_CLASSE: Client

ATTRI...
Fichier mémoire mis à jour avec 86 classes

Mise à jour du texte et mémoire: class 1:

NOM_CLASSE: A

ATTRIBUTS: 

Exemple: int

MÉTHODES:



class 2:

NOM_CLASSE: Client

ATTRI...
Fichier mémoire mis à jour avec 86 classes

Mise à jour du texte et mémoire: class 1:

NOM_CLASSE: A

ATTRIBUTS: 

Exemple: int

MÉTHODES:



class 2:

NOM_CLASSE: Client

ATTRI...
