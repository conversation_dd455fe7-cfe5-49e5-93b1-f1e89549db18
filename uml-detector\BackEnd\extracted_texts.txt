=== TEXTES EXTRAITS ===

class 1:
NOM_CLASSE: Student_Group
ATTRIBUTS: 
+Group Name : String
+Yearbook: Long
M�THODES: 
+Student_Group()
+~Student_Group()
+Edit() : Boolean
+StudentCount() : Long
+MaxCount() : Long

class 2:
NOM_CLASSE: Course
ATTRIBUTS: 
  Course Name : String
M�THODES: 
  Course()
  ~Course()
  Edit() : Boolean

class 3:
NOM_CLASSE: Grade
ATTRIBUTS: 
Value : String
M�THODES: 
Grade()
~Grade()
Edit() : Boolean

class 4:
NOM_CLASSE: Meeting
ATTRIBUTS: 
Term : Date
Place : String
Duration : Long
Subject : String
M�THODES: 
Meeting()
~Meeting()
Edit() : Boolean

class 5:
NOM_CLASSE: Attendance
ATTRIBUTS: 
-Presence : Boolean
-Notes : String
M�THODES: 
-Attendance()
-~Attendance()
-Edit() : Boolean

class 6:
NOM_CLASSE: Final_grade
ATTRIBUTS: 
Pass : Boolean
M�THODES: 
Final_grade()
~Final_grade()

class 7:
NOM_CLASSE: Employee
ATTRIBUTS: 
M�THODES: 
+ Employee()
+ ~Employee()

class 8:
NOM_CLASSE: Partial_grade
ATTRIBUTS: 
Notes : String
M�THODES: 
Partial_grade()
~Partial_grade()

class 9:
NOM_CLASSE: Student
ATTRIBUTS: 
+Album Number : String
M�THODES: 
+Student()
+~Student()

